"use client"

import { useState, useRef, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Mic, Upload, Loader2, CheckCircle2, AlertCircle, Music, Library, Plus } from "lucide-react"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { Progress } from "@/components/ui/progress"
import { EnhancedButton } from "@/components/ui/enhanced-button"
import { cn } from "@/lib/utils"
import { useTheme } from "next-themes"
import { useAuth } from "@/components/auth/auth-provider"
import AudioPlayer from 'react-h5-audio-player'
import 'react-h5-audio-player/lib/styles.css'
import styled, { createGlobalStyle } from 'styled-components'

// 使用styled-components创建全局样式
const AudioPlayerStyles = createGlobalStyle`
  .dark-player .rhap_container {
    background-color: #1f2937;
    box-shadow: none;
  }
  .dark-player .rhap_time,
  .dark-player .rhap_current-time,
  .dark-player .rhap_total-time {
    color: #9ca3af;
  }
  .dark-player .rhap_progress-indicator,
  .dark-player .rhap_volume-indicator {
    background: #3b82f6;
  }
  .dark-player .rhap_progress-filled {
    background: #3b82f6;
  }
  .dark-player .rhap_progress-bar,
  .dark-player .rhap_volume-bar {
    background: #4b5563;
  }
  .dark-player .rhap_play-pause-button {
    color: #3b82f6;
  }
  .dark-player .rhap_play-pause-button:hover {
    color: #60a5fa;
  }
  .dark-player .rhap_volume-button {
    color: #9ca3af;
  }
  .dark-player .rhap_main-controls-button {
    color: #9ca3af;
  }
  .dark-player .rhap_main-controls-button:hover {
    color: #f9fafb;
  }
`

// 使用styled-components创建样式化组件
const AudioPlayerContainer = styled.div`
  border-radius: 0.5rem;
  overflow: hidden;
`

export function VoiceCloneDialog({ open, onOpenChange, onVoiceCloned }) {
  // Theme
  const { theme } = useTheme()

  // State management
  const [activeTab, setActiveTab] = useState("create") // "create" or "library"

  // Create voice tab states
  const [voiceFile, setVoiceFile] = useState(null)
  const [voiceName, setVoiceName] = useState("")
  const [previewText, setPreviewText] = useState("Hello, this is a preview of my cloned voice. How does it sound?")
  const [previewCharCount, setPreviewCharCount] = useState(previewText.length)
  const [isGenerating, setIsGenerating] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [previewReady, setPreviewReady] = useState(false)
  const [error, setError] = useState(null)
  const [dragActive, setDragActive] = useState(false)
  const [cloneSuccess, setCloneSuccess] = useState(false)
  const [clonedVoiceId, setClonedVoiceId] = useState(null)
  const [isRecording, setIsRecording] = useState(false)
  const [recordingMode, setRecordingMode] = useState(false)
  const [isAudioPlaying, setIsAudioPlaying] = useState(false)
  const [audioUrl, setAudioUrl] = useState(null)

  // Voice library states
  const [voiceLibrary, setVoiceLibrary] = useState([])
  const [libraryLoading, setLibraryLoading] = useState(false)
  const [selectedVoiceFromLibrary, setSelectedVoiceFromLibrary] = useState(null)
  const [planInfo, setPlanInfo] = useState(null)

  const audioRef = useRef(null)
  const { supabase } = useAuth()

  // Handle voice name input
  const handleVoiceNameChange = (e) => {
    setVoiceName(e.target.value)
    if(e.target.value.length > 30) {
        setError("Voice name cannot exceed 30 characters.")
    } else {
        setError(null)
    }
  }

  // Handle voice file upload
  const handleVoiceFileChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0]
      // Basic file type validation
      if (!['audio/mpeg', 'audio/wav', 'audio/mp3', 'audio/mp4', 'audio/x-m4a'].includes(file.type)) {
        setError("Invalid file type. Please upload MP3, WAV, or M4A.")
        setVoiceFile(null)
        return
      }
      // Basic file size validation (e.g., max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        setError("File size exceeds 10MB. Please upload a smaller file.")
        setVoiceFile(null)
        return
      }
      setVoiceFile(file)
      setRecordingMode(false) // Ensure recording mode is off if a file is uploaded
      setError(null)
    }
  }

  // Handle voice file drag and drop
  const handleDrag = (e, active) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(active)
  }

  const handleDrop = (e) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const file = e.dataTransfer.files[0]
      // Basic file type validation
      if (!['audio/mpeg', 'audio/wav', 'audio/mp3', 'audio/mp4', 'audio/x-m4a'].includes(file.type)) {
        setError("Invalid file type. Please upload MP3, WAV, or M4A.")
        setVoiceFile(null)
        return
      }
      // Basic file size validation (e.g., max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        setError("File size exceeds 10MB. Please upload a smaller file.")
        setVoiceFile(null)
        return
      }
      setVoiceFile(file)
      setRecordingMode(false) // Ensure recording mode is off if a file is dropped
      setError(null)
    }
  }

  // Handle preview text change
  const handlePreviewTextChange = (e) => {
    const text = e.target.value
    if (text.length <= 1000) {
        setPreviewText(text)
        setPreviewCharCount(text.length)
        setError(null)
    } else {
        setError("Preview text cannot exceed 1000 characters.")
    }
  }

  // Start recording audio
  const startRecording = () => {
    setIsRecording(true)
    setRecordingMode(true)
    setVoiceFile(null) // Clear any uploaded file if starting recording
    setError(null)
    // Here you would implement the actual recording logic
    // For now, we'll just simulate it
    console.log("Recording started (simulated)")
  }

  // Stop recording audio
  const stopRecording = () => {
    setIsRecording(false)
    // Simulate a recorded file
    const mockFile = new File(["mock audio data"], "recording.mp3", { type: "audio/mp3" })
    setVoiceFile(mockFile)
    console.log("Recording stopped, mock file set:", mockFile)
  }

  // Fetch user voice library
  const fetchVoiceLibrary = async () => {
    setLibraryLoading(true)
    setError(null)

    try {
      const { data: { session } } = await supabase.auth.getSession()
      const accessToken = session?.access_token

      if (!accessToken) {
        setError("请先登录")
        return
      }

      const BACKEND_API_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_API_BASE_URL
      const response = await fetch(`${BACKEND_API_BASE_URL}/api/user-voices`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
        },
      })

      if (!response.ok) {
        throw new Error('获取音色库失败')
      }

      const data = await response.json()
      setVoiceLibrary(data.voice_clones || [])
      setPlanInfo(data.plan_info)

    } catch (error) {
      console.error('获取音色库失败:', error)
      setError('获取音色库失败，请稍后重试')
    } finally {
      setLibraryLoading(false)
    }
  }

  // Load voice library when dialog opens and library tab is active
  useEffect(() => {
    if (open && activeTab === "library") {
      fetchVoiceLibrary()
    }
  }, [open, activeTab])

  // Handle voice selection from library
  const handleSelectVoiceFromLibrary = (voice) => {
    setSelectedVoiceFromLibrary(voice)
    if (onVoiceCloned) {
      onVoiceCloned({
        voiceId: voice.minimax_voice_id,
        voiceName: voice.voice_name
      })
    }
    setTimeout(() => {
      onOpenChange(false)
    }, 500)
  }

  // Clone voice and generate preview
  const cloneVoiceAndPreview = async () => {
    if (!voiceFile) {
      setError("Please upload or record a voice sample file.")
      return
    }

    if (!voiceName.trim()) {
      setError("Please enter a name for your voice clone.")
      return
    }
    if (voiceName.trim().length > 30) {
        setError("Voice name cannot exceed 30 characters.")
        return
    }

    if (!previewText.trim()) {
      setError("Please enter preview text.")
      return
    }
    if (previewText.trim().length > 1000) {
        setError("Preview text cannot exceed 1000 characters.")
        return
    }


    setIsGenerating(true)
    setIsUploading(true)
    setError(null)
    setUploadProgress(0)
    setPreviewReady(false) // Reset preview ready state

    try {
      // Get access token
      const { data: { session } } = await supabase.auth.getSession()
      const accessToken = session?.access_token

      if (!accessToken) {
        setError("请先登录")
        return
      }

      const formData = new FormData()
      formData.append("audio_file", voiceFile)
      formData.append("voice_name", voiceName)
      formData.append("preview_text", previewText)

      const progressInterval = setInterval(() => {
        setUploadProgress((prev) => {
          if (prev >= 95) {
            clearInterval(progressInterval)
            return prev
          }
          return prev + 5
        })
      }, 200)

      const BACKEND_API_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_API_BASE_URL || "http://localhost:8000"

      const response = await fetch(`${BACKEND_API_BASE_URL}/api/clone-voice`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
        },
        body: formData,
      })

      clearInterval(progressInterval)
      setUploadProgress(100)

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ detail: "Failed to clone voice and parse error response." }))
        throw new Error(errorData.detail || "Failed to clone voice")
      }

      const data = await response.json()

      if (data.success && data.demo_audio_url) {
        setClonedVoiceId(data.voice_id)
        setAudioUrl(data.demo_audio_url) // 设置音频URL状态，AudioPlayer组件会自动使用这个URL
        console.log("Setting audio source to:", data.demo_audio_url)
        setPreviewReady(true)
      } else {
        throw new Error(data.detail || "No preview audio returned or operation failed.")
      }
    } catch (err) {
      console.error("Failed to clone voice:", err)
      setError(err.message || "Failed to clone voice. Please try again.")
    } finally {
      setIsGenerating(false)
      setIsUploading(false)
    }
  }

  // 不再需要单独的playPreview函数，因为AudioPlayer组件内部处理了播放/暂停逻辑

  // 添加一个简单的错误处理函数
  useEffect(() => {
    if (audioRef.current) {
      const player = audioRef.current;

      // 监听错误事件
      const handleError = (e) => {
        console.error("Audio Element Error:", e);
        setError("Failed to play audio. Please try again.");
      };

      // 添加事件监听器
      player.addEventListener('error', handleError);

      // 清理函数
      return () => {
        player.removeEventListener('error', handleError);
      };
    }
  }, [audioRef]);

  // Complete voice cloning
  const completeVoiceCloning = () => {
    if (!clonedVoiceId || !audioUrl) {
        setError("Cloning process not fully complete or preview URL missing.")
        return
    }
    setCloneSuccess(true)
    setError(null)

    if (onVoiceCloned) {
      onVoiceCloned({
        voiceId: clonedVoiceId,
        voiceName: voiceName,
        previewUrl: audioUrl // Use the state variable audioUrl
      })
    }

    setTimeout(() => {
      onOpenChange(false) // This will trigger handleDialogClose
    }, 2000)
  }

  // Reset state when dialog closes
  const handleDialogClose = (openState) => {
    if (!openState) {
      // 尝试暂停音频播放
      if (audioRef.current && audioRef.current.audio && audioRef.current.audio.current) {
        audioRef.current.audio.current.pause();
      }

      // Reset state after animation completes, or simply on close
      setTimeout(() => {
        setActiveTab("create")
        setVoiceFile(null)
        setVoiceName("")
        setPreviewText("Hello, this is a preview of my cloned voice. How does it sound?")
        setIsGenerating(false)
        setIsUploading(false)
        setUploadProgress(0)
        setPreviewReady(false)
        setError(null)
        setDragActive(false)
        setCloneSuccess(false)
        setClonedVoiceId(null)
        setIsRecording(false)
        setRecordingMode(false)
        setIsAudioPlaying(false)
        setAudioUrl(null)
        setVoiceLibrary([])
        setSelectedVoiceFromLibrary(null)
        setPlanInfo(null)
        setLibraryLoading(false)
      }, 300) // 延迟关闭动画
    }
    onOpenChange(openState)
  }

   // Update char count when previewText changes (e.g. on initial load)
   useEffect(() => {
    setPreviewCharCount(previewText.length);
  }, [previewText]);


  return (
    <Dialog open={open} onOpenChange={handleDialogClose}>
      <AudioPlayerStyles />
      <DialogContent className={cn(
        "sm:max-w-[600px] border transition-all duration-300",
        theme === "dark"
          ? "bg-gradient-to-b from-gray-900 to-black border-white/10 subtle-glass"
          : "bg-gradient-to-b from-gray-50 to-white border-gray-200 shadow-lg"
      )}>
        <DialogHeader>
          <DialogTitle className="text-2xl bg-clip-text text-transparent bg-gradient-to-r from-theme-blue to-purple-500 font-bold">
            Voice Cloning
          </DialogTitle>
          <DialogDescription className={theme === "dark" ? "text-gray-300" : "text-gray-600"}>
            Create your own AI voice clone or choose from your voice library
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-4">
          <TabsList className="grid w-full grid-cols-2 mb-6 bg-black/20 border border-white/10">
            <TabsTrigger
              value="create"
              className="data-[state=active]:bg-theme-blue data-[state=active]:text-white transition-all duration-300 flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              Create New Voice
            </TabsTrigger>
            <TabsTrigger
              value="library"
              className="data-[state=active]:bg-theme-blue data-[state=active]:text-white transition-all duration-300 flex items-center gap-2"
            >
              <Library className="h-4 w-4" />
              Voice Library
            </TabsTrigger>
          </TabsList>

          <TabsContent value="create" className="space-y-6 mt-0">
          {/* Voice Name Input */}
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <h3 className={cn(
              "text-sm font-medium mb-2 flex items-center",
              theme === "dark" ? "text-gray-200" : "text-gray-700"
            )}>
              <span className="flex items-center justify-center w-5 h-5 rounded-full bg-theme-blue/20 text-theme-blue mr-2 text-xs font-bold">1</span>
              Enter Voice Name
            </h3>
            <div className="relative group">
              <input
                type="text"
                value={voiceName}
                onChange={handleVoiceNameChange}
                placeholder="Enter a name for your voice clone (max 30 chars)"
                maxLength={30} // Enforce max length in input
                className={cn(
                  "w-full px-3 py-2 bg-background border rounded-md shadow-sm focus:outline-none transition-all duration-200",
                  "focus:ring-2 focus:ring-theme-blue/50 focus:border-theme-blue",
                  theme === "dark"
                    ? "border-white/10 hover:border-white/20 text-white"
                    : "border-gray-300 hover:border-gray-400 text-gray-900"
                )}
              />
              <p className="text-xs text-muted-foreground mt-1 flex justify-between">
                <span>Give your voice a unique name</span>
                <span className={voiceName.length > 25 ? "text-amber-500" : ""}>{voiceName.length} / 30</span>
              </p>
            </div>
          </motion.div>

          {/* Voice Sample Upload */}
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.1 }}
          >
            <h3 className={cn(
              "text-sm font-medium mb-2 flex items-center",
              theme === "dark" ? "text-gray-200" : "text-gray-700"
            )}>
              <span className="flex items-center justify-center w-5 h-5 rounded-full bg-theme-blue/20 text-theme-blue mr-2 text-xs font-bold">2</span>
              Upload Voice Sample
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* File Upload Option */}
              <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className={cn(
                  "border-2 border-dashed rounded-lg p-5 transition-all text-center cursor-pointer",
                  dragActive
                    ? "border-theme-blue bg-theme-blue/10"
                    : voiceFile && !recordingMode
                      ? "border-theme-blue bg-theme-blue/5"
                      : theme === "dark"
                        ? "border-white/10 hover:border-white/30 hover:bg-white/5"
                        : "border-gray-200 hover:border-gray-400 hover:bg-gray-50",
                )}
                onClick={() => !isRecording && document.getElementById("voiceFileUpload").click()} // Prevent click if recording
                onDragEnter={(e) => handleDrag(e, true)}
                onDragLeave={(e) => handleDrag(e, false)}
                onDragOver={(e) => handleDrag(e, true)}
                onDrop={handleDrop}
              >
                <input
                  type="file"
                  id="voiceFileUpload"
                  accept=".mp3,.wav, .m4a"
                  onChange={handleVoiceFileChange}
                  className="hidden"
                  disabled={isRecording} // Disable if recording
                />

                {voiceFile && !recordingMode ? (
                  <>
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ type: "spring", stiffness: 400, damping: 10 }}
                    >
                      <CheckCircle2 className="h-10 w-10 mx-auto mb-2 text-theme-blue" />
                    </motion.div>
                    <p className={cn(
                      "font-medium",
                      theme === "dark" ? "text-white" : "text-gray-900"
                    )}>File selected</p>
                    <p className="text-xs text-muted-foreground mt-1 truncate w-full px-2" title={voiceFile.name}>{voiceFile.name}</p>
                  </>
                ) : (
                  <>
                    <Upload className="h-10 w-10 mx-auto mb-2 text-muted-foreground" />
                    <p className={cn(
                      "font-medium",
                      theme === "dark" ? "text-white" : "text-gray-900"
                    )}>Upload File</p>
                    <p className="text-xs text-muted-foreground">
                      MP3/WAV/M4A format, max 10MB, 10-30s recommended
                    </p>
                  </>
                )}
              </motion.div>

              {/* Browser Recording Option */}
              <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className={cn(
                  "border-2 border-dashed rounded-lg p-5 transition-all text-center cursor-pointer",
                  recordingMode && voiceFile // When recording is done and file is set
                    ? "border-theme-blue bg-theme-blue/10"
                    : isRecording
                      ? "border-red-500 bg-red-500/10"
                      : theme === "dark"
                        ? "border-white/10 hover:border-white/30 hover:bg-white/5"
                        : "border-gray-200 hover:border-gray-400 hover:bg-gray-50",
                )}
                onClick={isRecording ? stopRecording : startRecording}
              >
                 {recordingMode && voiceFile ? ( // After recording is stopped and file is set
                    <>
                        <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ type: "spring", stiffness: 400, damping: 10 }}
                        >
                        <CheckCircle2 className="h-10 w-10 mx-auto mb-2 text-theme-blue" />
                        </motion.div>
                        <p className={cn("font-medium", theme === "dark" ? "text-white" : "text-gray-900")}>
                        Recording saved
                        </p>
                        <p className="text-xs text-muted-foreground mt-1 truncate w-full px-2" title={voiceFile.name}>{voiceFile.name}</p>
                    </>
                ) : (
                    <>
                        <Mic className={cn(
                        "h-10 w-10 mx-auto mb-2",
                        isRecording ? "text-red-500 animate-pulse" : "text-muted-foreground"
                        )} />
                        <p className={cn(
                        "font-medium",
                        theme === "dark" ? "text-white" : "text-gray-900"
                        )}>
                        {isRecording ? "Stop Recording" : "Record Audio"}
                        </p>
                        <p className="text-xs text-muted-foreground">
                        {isRecording ? "Recording..." : "Click to start"}
                        </p>
                    </>
                )}
              </motion.div>
            </div>
          </motion.div>

          {/* Preview Text Input */}
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.2 }}
          >
            <h3 className={cn(
              "text-sm font-medium mb-2 flex items-center",
              theme === "dark" ? "text-gray-200" : "text-gray-700"
            )}>
              <span className="flex items-center justify-center w-5 h-5 rounded-full bg-theme-blue/20 text-theme-blue mr-2 text-xs font-bold">3</span>
              Enter Text to Preview
            </h3>
            <div className="relative">
              <Textarea
                placeholder="Enter text to preview with your cloned voice..."
                className={cn(
                  "min-h-[100px] pr-16 border rounded-md shadow-sm focus:outline-none transition-all duration-200",
                  "focus:ring-2 focus:ring-theme-blue/50 focus:border-theme-blue resize-none",
                  theme === "dark"
                    ? "bg-background/50 border-white/10 hover:border-white/20 text-white"
                    : "bg-white border-gray-300 hover:border-gray-400 text-gray-900"
                )}
                value={previewText}
                onChange={handlePreviewTextChange}
                maxLength={1000} // Enforce max length
              />
              <div className={cn(
                "absolute bottom-2 right-2 text-xs px-2 py-1 rounded-md transition-colors",
                previewCharCount > 900
                  ? "text-amber-500 bg-amber-500/10"
                  : "text-muted-foreground bg-background/80"
              )}>
                {previewCharCount} / 1000 chars
              </div>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Enter the text you want to hear in your cloned voice
            </p>
          </motion.div>

          {/* Clone Voice Button */}
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.3 }}
          >
            <EnhancedButton
              onClick={cloneVoiceAndPreview}
              disabled={!voiceFile || !voiceName.trim() || voiceName.length > 30 || !previewText.trim() || previewText.length > 1000 || isGenerating || isUploading}
              className={cn(
                "w-full relative overflow-hidden group",
                isGenerating || isUploading
                  ? theme === "dark" ? "bg-gray-700" : "bg-gray-300"
                  : "bg-gradient-to-r from-theme-blue to-purple-600 text-white hover:opacity-90 shadow-md hover:shadow-lg"
              )}
              size="lg"
              color="blue"
            >
              <div className={cn(
                "absolute inset-0 bg-gradient-to-r from-theme-blue via-purple-600 to-theme-blue bg-[length:200%_100%]",
                "opacity-0 group-hover:opacity-100 transition-opacity",
                !isGenerating && !isUploading && "animate-gradient-x"
              )} />

              <div className="relative z-10">
                {isGenerating || isUploading ? (
                  <div className="flex items-center justify-center gap-2 py-1">
                    <Loader2 className="h-5 w-5 animate-spin" />
                    <span className="font-medium">{isUploading ? "Uploading..." : "Generating..."}</span>
                  </div>
                ) : (
                  <div className="flex items-center justify-center gap-2 py-1">
                    <Music className="h-5 w-5" />
                    <span className="font-medium">Clone Voice & Preview</span>
                  </div>
                )}
              </div>
            </EnhancedButton>
          </motion.div>

          {/* Upload Progress */}
          <AnimatePresence>
            {isUploading && !previewReady && ( // Show only during actual upload, not if preview is ready
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="space-y-2 overflow-hidden"
              >
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span className="flex items-center gap-1">
                    <Loader2 className="h-3 w-3 animate-spin" />
                    Processing voice sample...
                  </span>
                  <span className="font-medium">{uploadProgress}%</span>
                </div>
                <Progress
                  value={uploadProgress}
                  className={cn(
                    "h-2 transition-colors",
                    theme === "dark" ? "bg-gray-800" : "bg-gray-200"
                  )}
                  indicatorClassName="bg-theme-blue" // Theming for progress bar
                />
              </motion.div>
            )}
          </AnimatePresence>

          {/* Preview Section */}
          <AnimatePresence>
            {previewReady && (
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.95 }}
                transition={{ type: "spring", stiffness: 400, damping: 20 }}
                className="space-y-4"
              >
                <AudioPlayerContainer>
                  <AudioPlayer
                    ref={audioRef}
                    src={audioUrl}
                    autoPlay={false}
                    showJumpControls={false}
                    customAdditionalControls={[]}
                    layout="horizontal-reverse"
                    customProgressBarSection={[
                      "CURRENT_TIME",
                      "PROGRESS_BAR",
                      "DURATION",
                    ]}
                    onPlay={() => setIsAudioPlaying(true)}
                    onPause={() => setIsAudioPlaying(false)}
                    onEnded={() => setIsAudioPlaying(false)}
                    className={theme === "dark" ? "dark-player" : "light-player"}
                    style={{
                      backgroundColor: theme === "dark" ? "#1f2937" : "#f9fafb",
                      borderRadius: "0.5rem",
                      boxShadow: "none",
                      border: theme === "dark" ? "1px solid #374151" : "1px solid #e5e7eb",
                    }}
                    customVolumeControls={[]}
                    customIcons={{
                      play: <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-4 h-4"><polygon points="5 3 19 12 5 21 5 3"></polygon></svg>,
                      pause: <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-4 h-4"><rect x="6" y="4" width="4" height="16"></rect><rect x="14" y="4" width="4" height="16"></rect></svg>,
                    }}
                  />
                </AudioPlayerContainer>

                <EnhancedButton
                  onClick={completeVoiceCloning}
                  disabled={cloneSuccess || isGenerating || !previewReady}
                  className={cn(
                    "w-full",
                    cloneSuccess
                      ? theme === "dark" ? "bg-gray-700" : "bg-gray-300"
                      : "bg-gradient-to-r from-green-500 to-emerald-600 text-white hover:opacity-90 shadow-md"
                  )}
                  color="green"
                >
                  {cloneSuccess ? (
                    <div className="flex items-center justify-center gap-2">
                      <CheckCircle2 className="h-4 w-4" />
                      <span>Voice Cloned!</span>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center gap-2">
                      <CheckCircle2 className="h-4 w-4" />
                      <span>Use This Voice</span>
                    </div>
                  )}
                </EnhancedButton>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Error Message */}
          <AnimatePresence>
            {error && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className={cn(
                  "px-4 py-3 rounded-md text-sm flex items-start gap-2 shadow-md border",
                  theme === "dark"
                    ? "bg-red-900/20 border-red-900/50 text-red-400"
                    : "bg-red-50 border-red-200 text-red-600"
                )}
              >
                <AlertCircle className="h-5 w-5 shrink-0 mt-0.5" />
                <p>{error}</p>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Success Message (shown when cloneSuccess is true, typically after "Use This Voice") */}
          <AnimatePresence>
            {cloneSuccess && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className={cn(
                  "px-4 py-3 rounded-md text-sm flex items-center gap-2 shadow-md border",
                  theme === "dark"
                    ? "bg-green-900/20 border-green-900/50 text-green-400"
                    : "bg-green-50 border-green-200 text-green-600"
                )}
              >
                <CheckCircle2 className="h-5 w-5" />
                <div>
                  <p className="font-medium">Voice clone created successfully!</p>
                  <p className="text-xs opacity-80 mt-0.5">Voice ID: {clonedVoiceId}</p>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
          </TabsContent>

          <TabsContent value="library" className="space-y-6 mt-0">
            {libraryLoading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin text-theme-blue" />
                <span className="ml-2 text-muted-foreground">Loading voice library...</span>
              </div>
            ) : voiceLibrary.length === 0 ? (
              <div className="text-center py-8">
                <Library className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-medium mb-2">No voices in your library</h3>
                <p className="text-muted-foreground mb-4">
                  Create your first voice clone to get started
                </p>
                <EnhancedButton
                  onClick={() => setActiveTab("create")}
                  className="bg-theme-blue text-white hover:opacity-90"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Create Voice Clone
                </EnhancedButton>
              </div>
            ) : (
              <div className="space-y-4">
                {planInfo && (
                  <div className={cn(
                    "p-3 rounded-md border text-sm",
                    theme === "dark"
                      ? "bg-blue-900/20 border-blue-900/50 text-blue-400"
                      : "bg-blue-50 border-blue-200 text-blue-600"
                  )}>
                    <p>
                      Plan: <strong>{planInfo.plan_type}</strong> |
                      Voices: <strong>{planInfo.current_count}/{planInfo.max_limit}</strong>
                      {planInfo.can_create_more && (
                        <span className="text-green-500 ml-2">✓ Can create more</span>
                      )}
                    </p>
                  </div>
                )}

                <div className="grid gap-3">
                  {voiceLibrary.map((voice) => (
                    <motion.div
                      key={voice.id}
                      className={cn(
                        "border rounded-lg p-4 cursor-pointer transition-all hover:border-theme-blue/50",
                        theme === "dark"
                          ? "border-white/10 hover:bg-white/5"
                          : "border-gray-200 hover:bg-gray-50"
                      )}
                      onClick={() => handleSelectVoiceFromLibrary(voice)}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h4 className="font-medium text-lg">{voice.voice_name}</h4>
                          <p className="text-sm text-muted-foreground mt-1">
                            {voice.description}
                          </p>
                          <p className="text-xs text-muted-foreground mt-2">
                            Created: {new Date(voice.created_at).toLocaleDateString()}
                          </p>
                        </div>
                        <EnhancedButton
                          size="sm"
                          className="bg-theme-blue text-white hover:opacity-90"
                          onClick={(e) => {
                            e.stopPropagation()
                            handleSelectVoiceFromLibrary(voice)
                          }}
                        >
                          Use Voice
                        </EnhancedButton>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}