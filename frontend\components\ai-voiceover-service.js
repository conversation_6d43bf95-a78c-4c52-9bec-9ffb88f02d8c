"use client"

import { useState, useEffect, useRef } from "react"
import axios from "axios"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { EnhancedButton } from "@/components/ui/enhanced-button"
import { VoiceCloneDialog } from "@/components/voice-clone-dialog"
import {
  Mic,
  Upload,
  FileText,
  Play,
  Volume2,
  FileUp,
  Loader2,
  CheckCircle2,
  Download,
  Film,
  AlertCircle,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Maximize,
  UserPlus,
} from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { cn } from "@/lib/utils"
import { useAuth } from "@/components/auth/auth-provider"

export function AIVoiceoverService() {
  // State management
  const [currentStep, setCurrentStep] = useState(1)
  const [pdfFile, setPdfFile] = useState(null)
  const [txtFile, setTxtFile] = useState(null)
  const [scriptText, setScriptText] = useState("")
  const [scriptInputMethod, setScriptInputMethod] = useState("file") // "file" or "text"
  const [characterCount, setCharacterCount] = useState(0)
  const [selectedVoice, setSelectedVoice] = useState("male-qn-qingse")
  const [uploading, setUploading] = useState(false)
  const [taskId, setTaskId] = useState(null)
  const [taskStatus, setTaskStatus] = useState("idle")
  const [taskResultUrl, setTaskResultUrl] = useState(null)
  const [error, setError] = useState(null)
  const [progress, setProgress] = useState(0)
  const [dragActive, setDragActive] = useState({ pdf: false, txt: false })
  const [isAnimating, setIsAnimating] = useState(false)
  const [isVideoReady, setIsVideoReady] = useState(false)
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(60) // Assuming 60 seconds video

  // Voice cloning dialog states
  const [voiceCloneDialogOpen, setVoiceCloneDialogOpen] = useState(false)
  const [clonedVoiceId, setClonedVoiceId] = useState(null)
  const [hasClonedVoice, setHasClonedVoice] = useState(false)

  const videoRef = useRef(null)
  const playbackIntervalRef = useRef(null)

  const { user, loading, supabase } = useAuth();
  const router = typeof window !== 'undefined' ? require('next/router').useRouter() : null;
  const [showLoginAlert, setShowLoginAlert] = useState(false);

  // 登录拦截逻辑，仅用于上传区域
  const requireLogin = () => {
    if (!user && !loading) {
      setShowLoginAlert(true);
      setTimeout(() => {
        if (router) router.push('/login');
      }, 1200);
      return false;
    }
    return true;
  };

  // Handle file selection
  const handlePdfChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      setPdfFile(e.target.files[0])
    }
  }

  const handleTxtChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      setTxtFile(e.target.files[0])
    }
  }

  // Handle drag and drop
  const handleDrag = (e, type, active) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive((prev) => ({ ...prev, [type]: active }))
  }

  const handleDrop = (e, type) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive((prev) => ({ ...prev, [type]: false }))

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      if (type === "pdf") {
        setPdfFile(e.dataTransfer.files[0])
      } else if (type === "txt") {
        setTxtFile(e.dataTransfer.files[0])
      }
    }
  }

  // Handle script text input
  const handleScriptTextChange = (e) => {
    const text = e.target.value;
    setScriptText(text);
    setCharacterCount(text.length);

    // If text is entered, create a virtual text file
    if (text.trim()) {
      const blob = new Blob([text], { type: 'text/plain' });
      const file = new File([blob], "script.txt", { type: 'text/plain' });
      setTxtFile(file);
    } else {
      setTxtFile(null);
    }
  }

  // Handle script input method change
  const handleScriptInputMethodChange = (method) => {
    setScriptInputMethod(method);

    // Reset the other input method
    if (method === "file") {
      setScriptText("");
      setCharacterCount(0);
    } else {
      setTxtFile(null);
    }
  }

  // Handle voice selection
  const handleVoiceChange = (value) => {
    setSelectedVoice(value)
  }

  // Open voice clone dialog
  const openVoiceCloneDialog = () => {
    setVoiceCloneDialogOpen(true)
  }

  // Handle voice cloned callback
  const handleVoiceCloned = (cloneData) => {
    // 从对话框接收音色ID、名称和预览URL
    const { voiceId, voiceName, previewUrl } = cloneData;

    // 保存音色ID和预览URL
    setClonedVoiceId(voiceId);
    setHasClonedVoice(true);
    setSelectedVoice(voiceId); // 直接使用MiniMax的voice_id

    // 可以在这里保存预览URL，以便在需要时播放
    console.log("Voice clone preview URL:", previewUrl);

    // 显示成功消息 - 使用控制台记录而不是toast
    console.log("Voice Selected Successfully", `Using voice: ${voiceName} (ID: ${voiceId})`);
  }

  // Step navigation
  const nextStep = () => {
    setIsAnimating(true)
    setTimeout(() => {
      setCurrentStep(currentStep + 1)
      setIsAnimating(false)
    }, 300)
  }

  const prevStep = () => {
    setIsAnimating(true)
    setTimeout(() => {
      setCurrentStep(currentStep - 1)
      setIsAnimating(false)
    }, 300)
  }

  // Handle form submission
  const handleSubmit = async (e) => {
    if (e) e.preventDefault()

    // Client-side validation
    if (!pdfFile) {
      setError("Please select a PDF file")
      return
    }

    if (!txtFile && scriptInputMethod === "file") {
      setError("Please select a script file")
      return
    }

    if (!scriptText.trim() && scriptInputMethod === "text") {
      setError("Please enter script text")
      return
    }

    // Character count validation
    if (scriptInputMethod === "text" && characterCount > 5000) {
      setError("Script text exceeds the 5000 character limit")
      return
    }

    // Reset state
    setUploading(true)
    setError(null)
    setTaskId(null)
    setTaskStatus("queued")
    setTaskResultUrl(null)
    setProgress(25)

    // Prepare form data
    const formData = new FormData()
    formData.append("pdf_file", pdfFile)
    if (txtFile) {
      formData.append("txt_file", txtFile)
    }

    // Add voice-related data
    const presetVoices = ["male-qn-qingse", "male-qn-jingying", "female-shaonv", "female-yujie"];
    if (presetVoices.includes(selectedVoice)) {
      // Using preset voice
      formData.append("voice_type", "preset")
      formData.append("voice_option", selectedVoice)
    } else {
      // Using cloned voice (selectedVoice is the MiniMax voice_id)
      formData.append("voice_type", "clone")
      formData.append("voice_option", selectedVoice)
    }

    try {
      // 获取 access_token
      const { data: { session } } = await supabase.auth.getSession();
      const accessToken = session?.access_token;
      if (!accessToken) {
        setError("登录状态失效，请重新登录");
        setUploading(false);
        return;
      }

      // Send request to backend API
      const BACKEND_API_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_API_BASE_URL //|| "http://localhost:8000"

      console.log("Sending request to:", `${BACKEND_API_BASE_URL}/api/submit-job`)

      const response = await axios.post(`${BACKEND_API_BASE_URL}/api/submit-job`, formData, {
        headers: {
          "Content-Type": "multipart/form-data",
          "Authorization": `Bearer ${accessToken}`,
        },
      })

      // Handle successful response
      console.log("Task submitted successfully:", response.data)
      setTaskId(response.data.task_id)
      setTaskStatus("processing")
      setProgress(60)
    } catch (err) {
      // Handle error
      console.error("Failed to submit task:", err)
      // 确保错误是字符串而不是对象
      const errorMessage = err.response?.data?.detail
        ? (typeof err.response.data.detail === 'string'
            ? err.response.data.detail
            : JSON.stringify(err.response.data.detail))
        : "Failed to submit task, please try again"
      setError(errorMessage)
      setTaskStatus("failed")
    } finally {
      setUploading(false)
    }
  }

  // Poll task status
  useEffect(() => {
    let intervalId = null

    if (taskId && taskStatus !== "completed" && taskStatus !== "failed") {
      // Set polling interval
      intervalId = setInterval(async () => {
        try {
          const BACKEND_API_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_API_BASE_URL //|| "http://localhost:8000"
          const response = await axios.get(`${BACKEND_API_BASE_URL}/api/status/${taskId}`)

          // Update task status
          setTaskStatus(response.data.status)

          // Update progress
          if (response.data.status === "queued") setProgress(25)
          if (response.data.status === "processing") setProgress(60)
          if (response.data.status === "completed") {
            setProgress(100)
            setIsVideoReady(true)
          }

          // Handle completed status
          if (response.data.status === "completed") {
            if (intervalId) clearInterval(intervalId)

            // 优先使用Supabase URL，如果有的话
            if (response.data.supabase_url) {
              console.log("Using Supabase URL for preview:", response.data.supabase_url)
              setTaskResultUrl(response.data.supabase_url)
            } else {
              console.log("Using local URL for preview:", response.data.result_url)
              setTaskResultUrl(response.data.result_url)
            }
          }

          // Handle failed status
          if (response.data.status === "failed") {
            if (intervalId) clearInterval(intervalId)
            // 确保错误消息是字符串
            const errorMsg = response.data.error_message
              ? (typeof response.data.error_message === 'string'
                  ? response.data.error_message
                  : JSON.stringify(response.data.error_message))
              : "Processing failed, please try again"
            setError(errorMsg)
            setTaskStatus("failed")
          }
        } catch (err) {
          // Handle polling error
          console.error("Failed to get task status:", err)
          if (intervalId) clearInterval(intervalId)
          // 确保错误消息是字符串
          const errorMsg = err.response?.data?.detail
            ? (typeof err.response.data.detail === 'string'
                ? err.response.data.detail
                : JSON.stringify(err.response.data.detail))
            : "Failed to get task status, please refresh the page"
          setError(errorMsg)
          setTaskStatus("failed")
        }
      }, 3000) // Poll every 3 seconds
    }

    // Cleanup function
    return () => {
      if (intervalId) {
        clearInterval(intervalId)
      }
    }
  }, [taskId, taskStatus])

  // Handle result download
  const handleDownload = () => {
    if (taskResultUrl) {
      // 检查是否是Supabase URL
      if (taskResultUrl.includes('supabase') || taskResultUrl.startsWith('https://')) {
        // 如果是Supabase URL，直接使用它
        console.log("Downloading from Supabase URL:", taskResultUrl)
        window.location.href = taskResultUrl
      } else {
        // 如果是本地URL，构建下载路径
        const BACKEND_API_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_API_BASE_URL //|| "http://localhost:8000"

        // 构建下载URL - 只需在原始URL后添加/download
        const downloadUrl = `${taskResultUrl}/download`
        const fullUrl = `${BACKEND_API_BASE_URL}${downloadUrl}`

        console.log("Downloading from local URL:", fullUrl)
        // 直接导航到下载URL，浏览器会自动下载文件
        window.location.href = fullUrl
      }
    }
  }

  // Video player controls
  const togglePlayPause = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause()
        if (playbackIntervalRef.current) {
          clearInterval(playbackIntervalRef.current)
        }
      } else {
        videoRef.current.play()
        playbackIntervalRef.current = setInterval(() => {
          if (videoRef.current) {
            setCurrentTime(videoRef.current.currentTime)
          }
        }, 1000)
      }
      setIsPlaying(!isPlaying)
    }
  }

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs < 10 ? "0" : ""}${secs}`
  }

  // Animation variants
  const fadeVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5 } },
  }

  // File upload area component
  const FileUploadArea = ({ type, file, onChange, onDragEnter, onDragLeave, onDragOver, onDrop, isDragActive }) => (
    <motion.div
      className={cn(
        "border-2 border-dashed rounded-lg p-6 transition-all text-center cursor-pointer",
        isDragActive
          ? "border-theme-blue bg-theme-blue/5"
          : file
            ? "border-white/50 bg-white/5"
            : "border-muted-foreground/25 hover:border-white/30",
        "subtle-hover",
      )}
      onDragEnter={user ? onDragEnter : (e) => e.preventDefault()}
      onDragLeave={user ? onDragLeave : (e) => e.preventDefault()}
      onDragOver={user ? onDragOver : (e) => e.preventDefault()}
      onDrop={user ? onDrop : (e) => e.preventDefault()}
      onClick={() => {
        if (!requireLogin()) return;
        document.getElementById(`${type}File`).click();
      }}
      whileHover={{ scale: 1.01 }}
      transition={{ type: "spring", stiffness: 400, damping: 17 }}
    >
      <input
        type="file"
        id={`${type}File`}
        accept={type === "pdf" ? ".pdf" : ".txt"}
        onChange={user ? onChange : undefined}
        disabled={uploading || taskId !== null}
        className="hidden"
      />

      <div className="flex flex-col items-center gap-2">
        {file ? (
          <>
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ type: "spring", stiffness: 400, damping: 10 }}
            >
              <CheckCircle2 className="h-10 w-10 text-theme-blue" />
            </motion.div>
            <div>
              <p className="font-medium dark:text-white text-black">{file.name}</p>
              <p className="text-sm text-muted-foreground">{(file.size / 1024 / 1024).toFixed(2)} MB</p>
            </div>
          </>
        ) : (
          <>
            <motion.div
              className="p-3 rounded-full bg-white/10 mb-2"
              animate={{
                boxShadow: isDragActive
                  ? [
                      "0 0 0 0 rgba(33, 150, 243, 0)",
                      "0 0 10px 2px rgba(33, 150, 243, 0.3)",
                      "0 0 0 0 rgba(33, 150, 243, 0)",
                    ]
                  : "none",
              }}
              transition={{ duration: 2, repeat: isDragActive ? Number.POSITIVE_INFINITY : 0 }}
            >
              {type === "pdf" ? <FileUp className="h-6 w-6 dark:text-white text-black" /> : <FileText className="h-6 w-6 dark:text-white text-black" />}
            </motion.div>
            <p className="font-medium dark:text-white text-black">
              {type === "pdf" ? "Upload PPT File (PDF format)" : "Upload Script File (TXT format)"}
            </p>
            <p className="text-sm text-muted-foreground">Drag and drop file here or click to upload</p>
          </>
        )}
      </div>
    </motion.div>
  )

  // Voice option component
  const VoiceOption = ({ id, name, description, selected, onClick, disabled }) => (
    <motion.div
      className={cn(
        "border rounded-lg p-4 cursor-pointer transition-all",
        selected ? "border-theme-blue bg-theme-blue/10" : "hover:border-white/30 border-white/10 bg-black/20",
        disabled && "opacity-50 cursor-not-allowed",
        "subtle-hover",
      )}
      onClick={disabled ? undefined : onClick}
      whileHover={disabled ? {} : { scale: 1.02 }}
      transition={{ type: "spring", stiffness: 400, damping: 17 }}
    >
      <div className="flex items-start gap-3">
        <div
          className={cn(
            "mt-1 h-4 w-4 rounded-full border flex items-center justify-center",
            selected ? "border-theme-blue" : "border-muted-foreground",
          )}
        >
          {selected && (
            <motion.div
              className="h-2 w-2 rounded-full bg-theme-blue"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ type: "spring", stiffness: 400, damping: 10 }}
            />
          )}
        </div>
        <div>
          <p className="font-medium dark:text-white text-black">{name}</p>
          <p className="text-sm text-muted-foreground">{description}</p>
        </div>
      </div>
    </motion.div>
  )

  // Render error message
  const renderError = () => {
    if (error && !taskId) {
      // 确保错误是字符串
      const errorText = typeof error === 'string' ? error : JSON.stringify(error)

      return (
        <Alert variant="destructive" className="mt-4 border-red-900/50 bg-red-950/20">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{errorText}</AlertDescription>
        </Alert>
      )
    }
    return null
  }

  // Render result section
  const renderResult = () => {
    if (taskStatus === "completed" && taskResultUrl) {
      // 检查是否是Supabase URL
      let fullUrl = taskResultUrl
      if (!taskResultUrl.includes('supabase') && !taskResultUrl.startsWith('https://')) {
        // 如果不是Supabase URL，构建本地URL
        const BACKEND_API_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_API_BASE_URL //|| "http://localhost:8000"
        fullUrl = `${BACKEND_API_BASE_URL}${taskResultUrl}`
      }

      // 检查是否为视频文件，去除查询参数后检查
      const urlWithoutQuery = taskResultUrl.split('?')[0]
      const isVideo = urlWithoutQuery.toLowerCase().endsWith(".mp4")

      return (
        <motion.div
          className="space-y-8 py-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, ease: "easeOut" }}
        >
          <div className="text-center">
            <motion.div
              initial={{ scale: 0, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.5, type: "spring" }}
              className="w-20 h-20 rounded-full bg-green-500/20 flex items-center justify-center mx-auto mb-4"
            >
              <CheckCircle2 className="w-10 h-10 text-green-500" />
            </motion.div>
            <h3 className="text-xl font-medium mb-2 bg-clip-text text-transparent bg-gradient-to-r from-theme-blue to-purple-500">
              Your Voiceover is Ready!
            </h3>
            <p className="text-muted-foreground mb-8 max-w-md mx-auto">
              Your presentation has been successfully processed. Preview it below or download to use in your projects.
            </p>
          </div>

          <div>
            <h3 className="text-lg font-medium flex items-center gap-2 mb-4">
              <Film className="w-5 h-5" /> Preview Your Voiceover Presentation
            </h3>

            {/* Video preview container */}
            <div className="relative rounded-xl overflow-hidden bg-gradient-to-br from-gray-900 to-gray-800 aspect-video mb-6 shadow-xl border border-white/10">
              {isVideo ? (
                <video
                  ref={videoRef}
                  className="w-full h-full object-cover"
                  src={fullUrl}
                  onEnded={() => setIsPlaying(false)}
                  controls
                  poster="/preview-poster.jpg"
                />
              ) : (
                <div className="p-6 flex flex-col items-center justify-center h-full">
                  <Volume2 className="w-12 h-12 text-theme-blue mb-4" />
                  <audio
                    controls
                    src={fullUrl}
                    className="w-full max-w-md rounded-full"
                    style={{
                      accentColor: '#3B82F6',
                      backgroundColor: 'rgba(255,255,255,0.05)',
                      padding: '10px'
                    }}
                  ></audio>
                </div>
              )}
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 pt-2">
            <EnhancedButton
              onClick={handleDownload}
              className="flex-1 gap-2 bg-gradient-to-r from-theme-blue to-purple-600 text-white hover:opacity-90 py-3 shadow-lg"
            >
              <div className="flex items-center justify-center gap-2">
                <Download className="h-5 w-5" />
                <span className="font-medium">Download {isVideo ? "Video" : "Audio"} File</span>
              </div>
            </EnhancedButton>

            <Button
              variant="outline"
              className="flex-1 border-white/20 hover:bg-white/5 transition-all duration-300"
              onClick={() => {
                setCurrentStep(1);
                setPdfFile(null);
                setTxtFile(null);
                setScriptText("");
                setTaskId(null);
                setTaskStatus("idle");
                setTaskResultUrl(null);
              }}
            >
              <div className="flex items-center justify-center gap-2">
                <FileUp className="h-4 w-4" />
                <span>Create New Presentation</span>
              </div>
            </Button>
          </div>
        </motion.div>
      )
    }
    return null
  }

  // Next 按钮禁用逻辑：必须上传 PDF 且 TXT 或文本输入有内容
  const canGoNext = pdfFile && ((scriptInputMethod === "file" && txtFile) || (scriptInputMethod === "text" && scriptText.trim().length > 0));

  return (
    <Card className="w-full border-white/10 subtle-glass" id="processing-form">
      <CardHeader className="space-y-1">
        <CardTitle className="text-2xl gradient-text">AI Voiceover Service</CardTitle>
        <CardDescription>
          Upload your PPT file and script, select a suitable voice, and we'll generate a professional AI voiceover
        </CardDescription>

        {/* Progress indicator */}
        <div className="flex justify-between items-center mt-4 px-2">
          <div className={`flex flex-col items-center ${currentStep >= 1 ? "text-theme-blue" : "text-gray-500"}`}>
            <div
              className={`w-8 h-8 rounded-full flex items-center justify-center border-2 ${
                currentStep >= 1 ? "border-theme-blue bg-theme-blue/20" : "border-gray-500"
              } transition-all duration-300`}
            >
              1
            </div>
            <span className="text-xs mt-1">Upload Files</span>
          </div>
          <div
            className={`flex-1 h-1 mx-2 ${
              currentStep >= 2 ? "bg-theme-blue" : "bg-gray-700"
            } transition-all duration-500`}
          ></div>
          <div className={`flex flex-col items-center ${currentStep >= 2 ? "text-theme-blue" : "text-gray-500"}`}>
            <div
              className={`w-8 h-8 rounded-full flex items-center justify-center border-2 ${
                currentStep >= 2 ? "border-theme-blue bg-theme-blue/20" : "border-gray-500"
              } transition-all duration-300`}
            >
              2
            </div>
            <span className="text-xs mt-1">Select Voice</span>
          </div>
          <div
            className={`flex-1 h-1 mx-2 ${
              currentStep >= 3 ? "bg-theme-blue" : "bg-gray-700"
            } transition-all duration-500`}
          ></div>
          <div className={`flex flex-col items-center ${currentStep >= 3 ? "text-theme-blue" : "text-gray-500"}`}>
            <div
              className={`w-8 h-8 rounded-full flex items-center justify-center border-2 ${
                currentStep >= 3 ? "border-theme-blue bg-theme-blue/20" : "border-gray-500"
              } transition-all duration-300`}
            >
              3
            </div>
            <span className="text-xs mt-1">Preview & Export</span>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        {/* Step 1: Upload Files */}
        {currentStep === 1 && (
          <motion.div
            className="space-y-6"
            initial="hidden"
            animate={isAnimating ? "hidden" : "visible"}
            variants={fadeVariants}
          >
            <div>
              <h3 className="text-lg font-medium flex items-center gap-2 mb-3">
                <FileUp className="w-5 h-5" /> PPT File Upload
              </h3>
              <FileUploadArea
                type="pdf"
                file={pdfFile}
                onChange={handlePdfChange}
                onDragEnter={(e) => handleDrag(e, "pdf", true)}
                onDragLeave={(e) => handleDrag(e, "pdf", false)}
                onDragOver={(e) => handleDrag(e, "pdf", true)}
                onDrop={(e) => handleDrop(e, "pdf")}
                isDragActive={dragActive.pdf}
              />
            </div>

            <Separator className="bg-gray-800" />

            <div>
              <h3 className="text-lg font-medium flex items-center gap-2 mb-3">
                <FileText className="w-5 h-5" /> Script Input
              </h3>

              <Tabs defaultValue="file" onValueChange={(value) => handleScriptInputMethodChange(value)}>
                <TabsList className="grid w-full grid-cols-2 mb-4 bg-black/20 border border-white/10">
                  <TabsTrigger
                    value="file"
                    className="data-[state=active]:bg-theme-blue data-[state=active]:text-white transition-all duration-300"
                  >
                    Upload File
                  </TabsTrigger>
                  <TabsTrigger
                    value="text"
                    className="data-[state=active]:bg-theme-blue data-[state=active]:text-white transition-all duration-300"
                  >
                    Enter Text
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="file">
                  <FileUploadArea
                    type="txt"
                    file={txtFile}
                    onChange={handleTxtChange}
                    onDragEnter={(e) => handleDrag(e, "txt", true)}
                    onDragLeave={(e) => handleDrag(e, "txt", false)}
                    onDragOver={(e) => handleDrag(e, "txt", true)}
                    onDrop={(e) => handleDrop(e, "txt")}
                    isDragActive={dragActive.txt}
                  />
                </TabsContent>

                <TabsContent value="text">
                  <div className="relative">
                    <Textarea
                      placeholder={`Enter your script text here.
Use <Slide N> tags to mark slide transitions paragraph:

<Slide 1>
some text
<Slide 2>
some text`}
                      className="min-h-[150px] bg-background border-input pr-16 dark:text-white text-black"
                      value={scriptText}
                      onChange={handleScriptTextChange}
                    />
                    <div className="absolute bottom-2 right-2 text-xs text-muted-foreground bg-background/80 px-2 py-1 rounded-md">
                      {characterCount} / 5000 chars
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
            </div>
          </motion.div>
        )}

        {/* Step 2: Select Voice */}
        {currentStep === 2 && (
          <motion.div
            className="space-y-6"
            initial="hidden"
            animate={isAnimating ? "hidden" : "visible"}
            variants={fadeVariants}
          >
            <div>
              <h3 className="text-lg font-medium flex items-center gap-2 mb-3">
                <Volume2 className="w-5 h-5" /> Voice Selection
              </h3>

              <div className="space-y-4">
                <h4 className="text-sm font-medium mb-3">Select a Voice</h4>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <VoiceOption
                    id="male-qn-qingse"
                    name="Young Male"
                    description="Youthful and energetic, suitable for educational content"
                    selected={selectedVoice === "male-qn-qingse"}
                    onClick={() => handleVoiceChange("male-qn-qingse")}
                    disabled={uploading || taskId !== null}
                  />
                  <VoiceOption
                    id="male-qn-jingying"
                    name="Professional Male"
                    description="Mature and authoritative, ideal for business presentations"
                    selected={selectedVoice === "male-qn-jingying"}
                    onClick={() => handleVoiceChange("male-qn-jingying")}
                    disabled={uploading || taskId !== null}
                  />
                  <VoiceOption
                    id="female-shaonv"
                    name="Young Female"
                    description="Fresh and sweet, suitable for lifestyle content"
                    selected={selectedVoice === "female-shaonv"}
                    onClick={() => handleVoiceChange("female-shaonv")}
                    disabled={uploading || taskId !== null}
                  />
                  <VoiceOption
                    id="female-yujie"
                    name="Professional Female"
                    description="Elegant and sophisticated, perfect for formal presentations"
                    selected={selectedVoice === "female-yujie"}
                    onClick={() => handleVoiceChange("female-yujie")}
                    disabled={uploading || taskId !== null}
                  />

                  {/* Voice Clone Option */}
                  <div
                    className={cn(
                      "border rounded-lg p-4 cursor-pointer transition-all",
                      !["male-qn-qingse", "male-qn-jingying", "female-shaonv", "female-yujie"].includes(selectedVoice)
                        ? "border-theme-blue bg-theme-blue/10"
                        : "hover:border-white/30 border-white/10 bg-black/20",
                      (uploading || taskId !== null) && "opacity-50 cursor-not-allowed",
                      "subtle-hover col-span-1 sm:col-span-2",
                    )}
                    onClick={() => {
                      if (!requireLogin()) return;
                      openVoiceCloneDialog();
                    }}
                  >
                    <div className="flex items-start gap-3">
                      <div
                        className={cn(
                          "mt-1 h-4 w-4 rounded-full border flex items-center justify-center",
                          !["male-qn-qingse", "male-qn-jingying", "female-shaonv", "female-yujie"].includes(selectedVoice)
                            ? "border-theme-blue"
                            : "border-muted-foreground",
                        )}
                      >
                        {!["male-qn-qingse", "male-qn-jingying", "female-shaonv", "female-yujie"].includes(selectedVoice) && (
                          <motion.div
                            className="h-2 w-2 rounded-full bg-theme-blue"
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            transition={{ type: "spring", stiffness: 400, damping: 10 }}
                          />
                        )}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <p className="font-medium dark:text-white text-black">Voice Clones</p>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-7 px-2 text-xs text-theme-blue hover:bg-theme-blue/10"
                            onClick={(e) => {
                              e.stopPropagation();
                              openVoiceCloneDialog();
                            }}
                            disabled={uploading || taskId !== null}
                          >
                            <UserPlus className="h-3 w-3 mr-1" />
                            Manage
                          </Button>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {hasClonedVoice
                            ? `Using cloned voice (ID: ${clonedVoiceId?.slice(0, 20)}...)`
                            : "Create and manage your voice clones"}
                        </p>
                        <p className="text-xs text-theme-blue mt-2 flex items-center gap-1">
                          <Mic className="h-3 w-3" />
                          Click to open voice library
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Voice Clone Dialog */}
            <VoiceCloneDialog
              open={voiceCloneDialogOpen}
              onOpenChange={setVoiceCloneDialogOpen}
              onVoiceCloned={handleVoiceCloned}
            />
          </motion.div>
        )}

        {/* Step 3: Processing and Preview */}
        {currentStep === 3 && (
          <motion.div
            className="space-y-6"
            initial="hidden"
            animate={isAnimating ? "hidden" : "visible"}
            variants={fadeVariants}
          >
            {(taskStatus === "queued" || taskStatus === "processing" || taskStatus === "idle") && (
              <div className="py-10 text-center">
                <motion.div
                  className="w-24 h-24 rounded-full bg-gradient-to-r from-theme-blue/20 to-purple-500/20 flex items-center justify-center mx-auto mb-6"
                  initial={{ scale: 0.8 }}
                  animate={{ scale: 1 }}
                  transition={{
                    duration: 0.5,
                    repeat: Number.POSITIVE_INFINITY,
                    repeatType: "reverse",
                  }}
                >
                  <Loader2 className="w-12 h-12 text-theme-blue animate-spin" />
                </motion.div>
                <h3 className="text-xl font-medium mb-2 bg-clip-text text-transparent bg-gradient-to-r from-theme-blue to-purple-500">
                  Processing Your Voiceover
                </h3>
                <p className="text-muted-foreground mb-6">
                  We're generating your AI voiceover and syncing it with your presentation
                </p>

                {/* Progress bar */}
                <div className="relative max-w-md mx-auto">
                  <Progress
                    value={progress}
                    className="h-3 bg-white/10 rounded-full overflow-hidden"
                    indicatorClassName={cn(
                      "transition-all duration-300 bg-gradient-to-r from-theme-blue to-purple-500"
                    )}
                  />
                  <div className="absolute -top-1 left-0 right-0 flex justify-between text-xs text-muted-foreground">
                    <span>Uploading</span>
                    <span>Processing</span>
                    <span>Finalizing</span>
                  </div>
                </div>
                <p className="text-sm text-muted-foreground mt-6 flex items-center justify-center gap-2">
                  <span className="inline-block h-2 w-2 rounded-full bg-theme-blue animate-pulse"></span>
                  {progress}% Complete
                </p>
              </div>
            )}

            {taskStatus === "failed" && (
              <div className="py-10 text-center">
                <motion.div
                  className="w-24 h-24 rounded-full bg-red-500/20 flex items-center justify-center mx-auto mb-6"
                  initial={{ scale: 0.9 }}
                  animate={{ scale: 1 }}
                  transition={{
                    duration: 0.5,
                    repeat: 3,
                    repeatType: "reverse"
                  }}
                >
                  <AlertCircle className="w-12 h-12 text-red-500" />
                </motion.div>
                <h3 className="text-xl font-medium mb-2">Processing Failed</h3>
                <p className="text-red-400 mb-6">
                  {typeof error === 'string' ? error : (error ? JSON.stringify(error) : "An error occurred during processing. Please try again.")}
                </p>
                <EnhancedButton
                  className="bg-gradient-to-r from-theme-blue to-purple-600 text-white hover:opacity-90 px-8 py-3 shadow-lg"
                  onClick={() => {
                    setCurrentStep(2);
                    setTaskStatus("idle");
                    setError(null);
                  }}
                >
                  <div className="flex items-center gap-2">
                    <span className="font-medium">Go Back & Try Again</span>
                  </div>
                </EnhancedButton>
              </div>
            )}

            {taskStatus === "completed" && renderResult()}
          </motion.div>
        )}

        {renderError()}
      </CardContent>

      <CardFooter className="flex justify-between">
        {currentStep > 1 && !isVideoReady && (
          <Button variant="outline" onClick={prevStep} className="transition-all duration-300 hover:scale-105">
            Previous
          </Button>
        )}
        {currentStep === 1 && <div></div>}
        {isVideoReady && <div></div>}

        {currentStep === 1 ? (
          <EnhancedButton
            className="bg-white text-black hover:bg-white/90"
            color="blue"
            onClick={nextStep}
            disabled={!canGoNext}
          >
            Next
          </EnhancedButton>
        ) : currentStep === 2 ? (
          <EnhancedButton
            className="bg-gradient-to-r from-theme-blue to-purple-600 text-white hover:opacity-90 px-8 py-3 shadow-lg"
            onClick={() => {
              handleSubmit();
              nextStep();
            }}
            disabled={uploading}
          >
            <div className="flex items-center gap-2">
              <Volume2 className="h-5 w-5" />
              <span className="font-medium">Generate Voiceover</span>
            </div>
          </EnhancedButton>
        ) : (
          <div></div>
        )}
      </CardFooter>

      {/* Login alert */}
      {showLoginAlert && (
        <Alert variant="destructive" className="fixed top-8 left-1/2 -translate-x-1/2 z-50 w-full max-w-xs border-red-900/50 bg-red-950/80">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Please login</AlertTitle>
          <AlertDescription>Please log in to use the voiceover feature.</AlertDescription>
        </Alert>
      )}
    </Card>
  )
}